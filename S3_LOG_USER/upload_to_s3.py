#!/usr/bin/env python3
"""
Script para subir archivos CSV generados a S3
"""

import boto3
import os
from pathlib import Path
from datetime import datetime

def upload_csv_files_to_s3(fecha_str="2025-06-10"):
    """Sube archivos CSV a S3 en la estructura correcta"""
    
    # Configurar cliente S3
    s3_client = boto3.client('s3')
    
    # Bucket de destino (golden reporte final zone)
    bucket = "prd-datalake-golden-reporte-final-zone-637423440311"
    
    # Convertir fecha
    fecha_obj = datetime.strptime(fecha_str, '%Y-%m-%d')
    fecha_siguiente = fecha_obj.replace(day=fecha_obj.day + 1)
    fecha_s3 = fecha_siguiente.strftime('%Y-%m-%d')  # Fecha + 1 día para S3
    
    # Buscar archivos CSV generados (patrón corregido)
    fecha_format = fecha_str.replace('-', '')
    csv_pattern = f"**/LOGUSR-*-{fecha_format}*.csv"
    archivos_encontrados = list(Path('.').glob(csv_pattern))

    # Si no encuentra con el patrón anterior, buscar con timestamp
    if not archivos_encontrados:
        csv_pattern = f"**/LOGUSR-*{fecha_format}*.csv"
        archivos_encontrados = list(Path('.').glob(csv_pattern))
    
    print(f"🔍 Buscando archivos: {csv_pattern}")
    print(f"📁 Archivos encontrados: {len(archivos_encontrados)}")
    
    archivos_subidos = []
    
    for archivo_local in archivos_encontrados:
        try:
            # Extraer banco del nombre del archivo
            nombre_archivo = archivo_local.name
            if 'LOGUSR-' in nombre_archivo:
                # Formato: LOGUSR-BANCO-FECHA.csv
                partes = nombre_archivo.split('-')
                if len(partes) >= 3:
                    banco = partes[1]
                    
                    # Construir ruta S3: BANCO/YYYY-MM-DD/archivo.csv
                    s3_key = f"{banco}/{fecha_s3}/{nombre_archivo}"
                    
                    # Subir archivo
                    print(f"📤 Subiendo: {archivo_local} → s3://{bucket}/{s3_key}")
                    
                    s3_client.upload_file(
                        str(archivo_local),
                        bucket,
                        s3_key,
                        ExtraArgs={
                            'ContentType': 'text/csv',
                            'Metadata': {
                                'fecha_proceso': fecha_str,
                                'banco': banco,
                                'tipo': 'log_usuarios'
                            }
                        }
                    )
                    
                    archivos_subidos.append(f"s3://{bucket}/{s3_key}")
                    print(f"✅ Subido exitosamente")
                    
        except Exception as e:
            print(f"❌ Error subiendo {archivo_local}: {e}")
    
    print(f"\n🎉 Proceso completado:")
    print(f"📊 Total archivos subidos: {len(archivos_subidos)}")
    for archivo in archivos_subidos:
        print(f"  📁 {archivo}")
    
    return archivos_subidos

if __name__ == "__main__":
    import sys
    fecha = sys.argv[1] if len(sys.argv) > 1 else "2025-06-10"
    upload_csv_files_to_s3(fecha)
