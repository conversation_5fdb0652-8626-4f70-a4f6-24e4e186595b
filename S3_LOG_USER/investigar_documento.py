#!/usr/bin/env python3
import duckdb
import boto3
import sys

# Configurar DuckDB con S3
conn = duckdb.connect()
session = boto3.Session()
credentials = session.get_credentials().get_frozen_credentials()

conn.sql("INSTALL httpfs;")
conn.sql("LOAD httpfs;")
conn.sql("SET s3_region='us-east-1';")
conn.sql("SET s3_use_ssl=true;")
conn.sql("SET s3_url_style='path';")
conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
if credentials.token:
    conn.sql(f"SET s3_session_token='{credentials.token}';")

# Buscar documento ******** en USER_AUTH_CHANGE_HISTORY
print("=== BUSCANDO ******** EN USER_AUTH_CHANGE_HISTORY ===")
query = """
SELECT 
    CREATED_ON,
    USER_ID,
    ACCOUNT_ID,
    REQUEST_TYPE,
    CAST(CREATED_ON AS DATE) as fecha_dia
FROM read_parquet('s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet')
WHERE USER_ID LIKE '%********%' 
   OR ACCOUNT_ID LIKE '%********%'
   OR CREATED_ON::DATE = '2025-06-09'::DATE
ORDER BY CREATED_ON
LIMIT 10
"""

try:
    result = conn.execute(query).fetchall()
    print(f"Registros encontrados: {len(result)}")
    for row in result:
        print(f"  {row}")
except Exception as e:
    print(f"Error: {e}")

# Buscar en USER_IDENTIFIER
print("\n=== BUSCANDO ******** EN USER_IDENTIFIER ===")
query2 = """
SELECT 
    USER_ID,
    IDENTIFIER_VALUE,
    IDENTIFIER_TYPE
FROM read_parquet('s3://prd-datalake-silver-zone-************/PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet')
WHERE IDENTIFIER_VALUE = '********'
LIMIT 5
"""

try:
    result2 = conn.execute(query2).fetchall()
    print(f"Registros encontrados: {len(result2)}")
    for row in result2:
        print(f"  {row}")
except Exception as e:
    print(f"Error: {e}")

